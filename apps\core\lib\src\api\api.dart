import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:core/core.dart';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_cors_headers/shelf_cors_headers.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:sqflite/sqflite.dart';

/// HTTP API server for the node
class ApiServer {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  EventService? _eventService;

  HttpServer? _server;
  Router? _router;

  ApiServer(this._databaseManager, this._nodeId);

  /// Set the event service (called after initialization)
  void setEventService(EventService eventService) {
    _eventService = eventService;
  }

  /// Start the API server
  Future<void> start({int port = Configuration.defaultApiPort}) async {
    if (_server != null) return;

    _router = Router();
    _setupRoutes();

    final handler = Pipeline()
        .addMiddleware(corsHeaders())
        .addMiddleware(logRequests())
        .addHandler(_router!);

    _server = await shelf_io.serve(handler, InternetAddress.anyIPv4, port);
    print('API server started on port ${_server!.port}');
  }

  /// Stop the API server
  Future<void> stop() async {
    await _server?.close();
    _server = null;
    _router = null;
  }

  /// Setup API routes
  void _setupRoutes() {
    _router!.get('/', _getNodeInfo);
    _router!.get('/health', _healthCheck);

    _router!.get('/pull', _getEvents);
    _router!.post('/push', _receiveEvents);

    _router!.get('/api/tasks', _getTasks);
  }

  /// Health check endpoint
  Response _healthCheck(Request request) {
    return Response.ok(
      jsonEncode({
        'status': 'live',
        'nodeId': _nodeId,
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Get node information
  Response _getNodeInfo(Request request) {
    return Response.ok(
      jsonEncode({
        'node_id': _nodeId,
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Pull events and records from this node
  Future<Response> _getEvents(Request request) async {
    try {
      final sinceParam = request.url.queryParameters['since'];
      int sinceTimestamp = 0;

      if (sinceParam != null) {
        sinceTimestamp = int.tryParse(sinceParam) ?? 0;
      }

      // Get events after the specified timestamp
      final events = await _databaseManager.getEventsAfterTimestamp(sinceTimestamp.toString());
      final eventsJson = events.map((event) => event.toJson()).toList();

      // Get records after the specified timestamp
      final records = await _databaseManager.getRecordsAfterTimestamp(sinceTimestamp);
      final recordsJson = records.map((record) => record.toJson()).toList();

      return Response.ok(
        jsonEncode({
          'events': eventsJson,
          'records': recordsJson,
          'events_count': events.length,
          'records_count': records.length,
          'node_id': _nodeId,
          'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000, // UTC timestamp in seconds
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to pull events and records: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Push events and records to this node
  Future<Response> _receiveEvents(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Process events
      final eventsData = data['events'] as List<dynamic>? ?? [];
      final events = eventsData
          .map((eventData) => Event.fromJson(eventData as Map<String, dynamic>))
          .toList();

      // Process records
      final recordsData = data['records'] as List<dynamic>? ?? [];
      final records = recordsData
          .map((recordData) => Record.fromJson(recordData as Map<String, dynamic>))
          .toList();

      // Use EventService to insert received data if available
      if (_eventService != null) {
        await _eventService!.insertReceivedData(
          events: events,
          records: records,
        );

        // Calculate counts for response
        final externalEvents = events.where((event) => event.origin != _nodeId).toList();
        final externalEventIds = externalEvents.map((e) => e.id).toSet();
        final externalRecords = records
            .where((record) => externalEventIds.contains(record.eventId))
            .toList();

        final insertedEvents = externalEvents.length;
        final insertedRecords = externalRecords.length;

        return Response.ok(
          jsonEncode({
            'events_received': insertedEvents,
            'records_received': insertedRecords,
            'events_filtered': events.length - externalEvents.length,
            'records_filtered': records.length - externalRecords.length,
            'status': 'success',
            'node_id': _nodeId,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } else {
        // Fallback to direct database insertion if EventService not available
        return Response.internalServerError(
          body: jsonEncode({'error': 'EventService not available'}),
          headers: {'Content-Type': 'application/json'},
        );
      }
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'error': 'Failed to push events and records: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get current tasks (reconstructed from events)
  Future<Response> _getTasks(Request request) async {
    try {
      // This is a simplified version - in a real implementation,
      // you'd use the EventStore to reconstruct task states
      final allEvents = await _databaseManager.getAllEvents();
      final taskEvents =
          allEvents.where((event) => event.entityType == 'task').toList();

      // Group by entity_id and reconstruct states
      final Map<String, List<Event>> taskGroups = {};
      for (final event in taskEvents) {
        taskGroups.putIfAbsent(event.entityId, () => []).add(event);
      }

      final List<Map<String, dynamic>> tasks = [];
      for (final entry in taskGroups.entries) {
        final taskState = _reconstructTaskState(entry.key, entry.value);
        if (taskState != null) {
          tasks.add(taskState);
        }
      }

      return Response.ok(
        jsonEncode({
          'tasks': tasks,
          'count': tasks.length,
          'node_id': _nodeId,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get tasks: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Reconstruct task state from events (simplified version)
  Map<String, dynamic>? _reconstructTaskState(
      String taskId, List<Event> events) {
    if (events.isEmpty) return null;

    // Sort events by timestamp
    events.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    Map<String, dynamic> state = {};

    for (final event in events) {
      switch (event.eventType) {
        case 'task_created':
          state = {
            'id': taskId,
            'title': event.payload['title'],
            'description': event.payload['description'] ?? '',
            'completed': false,
            'created_at': event.timestamp.toIso8601String(),
            'updated_at': event.timestamp.toIso8601String(),
          };
          break;

        case 'task_completed':
          state['completed'] = true;
          state['completed_at'] = event.timestamp.toIso8601String();
          state['updated_at'] = event.timestamp.toIso8601String();
          break;

        case 'task_deleted':
          return null; // Task was deleted
      }
    }

    return state.isEmpty ? null : state;
  }
}
